{"parent_ticket": {"key": "SMI-28008", "fields": {"summary": "CVE-2023-44487 HTTP/2 Rapid Reset vulnerability in multiple repositories", "description": "Security vulnerability CVE-2023-44487 affects multiple Docker images and repositories.\n                \nAffected Docker Images:\n- dockerhub.cisco.com/smi-fuse-docker-internal/nginx:1.20.1\n- dockerhub.cisco.com/smi-fuse-docker-internal/golang:1.19-alpine\n- docker.io/nginx:1.20.1\n\nAffected Repositories:\n- wwwin-github.cisco.com/smi-fuse/web-frontend\n- wwwin-github.cisco.com/smi-fuse/api-gateway\n- wwwin-github.cisco.com/smi-fuse/auth-service\n\nCVE Details:\n- CVE ID: CVE-2023-44487\n- Component: HTTP/2 implementation\n- Severity: HIGH\n- CVSS Score: 7.5\n- Description: HTTP/2 Rapid Reset vulnerability allowing DoS attacks\n", "status": {"name": "In Progress"}, "components": [{"name": "5gaas-security"}]}}, "cve_info": {"cve_id": "CVE-2023-44487", "component": null, "severity": "HIGH", "description": "Security vulnerability CVE-2023-44487 affects multiple Docker images and repositories.\n                \nAffected Docker Images:\n- dockerhub.cisco.com/smi-fuse-docker-internal/nginx:1.20.1\n- dockerhub.cisco.com/smi-fuse-docker-internal/golang:1.19-alpine\n- docker.io/nginx:1.20.1\n\nAffected Repositories:\n- wwwin-github.cisco.com/smi-fuse/web-frontend\n- wwwin-github.cisco.com/smi-fuse/api-gateway\n- wwwin-github.cisco.com/smi-fuse/auth-service\n\nCVE Details:\n- CVE ID: CVE-2023-44487\n- Component: HTTP/2 implementation\n- Severity: HIGH\n- CVSS Score: 7.5\n- Description: HTTP/2 Rapid Reset vulnerability allowing DoS attacks\n", "docker_images": ["dockerhub.cisco.com/smi-fuse-docker-internal/nginx:1.20.1", "dockerhub.cisco.com/smi-fuse-docker-internal/golang:1.19-alpine", "docker.io/nginx:1.20.1"], "repositories": ["wwwin-github.cisco.com/smi-fuse/web-frontend", "wwwin-github.cisco.com/smi-fuse/api-gateway", "wwwin-github.cisco.com/smi-fuse/auth-service"]}, "subtasks": [{"key": "SMI-28009", "fields": {"summary": "Fix CVE-2023-44487 in web-frontend repository", "description": "Repository: wwwin-github.cisco.com/smi-fuse/web-frontend\nDocker Image: dockerhub.cisco.com/smi-fuse-docker-internal/nginx:1.20.1", "status": {"name": "To Do"}, "assignee": {"name": "cve-analysis-agent"}}}, {"key": "SMI-28010", "fields": {"summary": "Fix CVE-2023-44487 in api-gateway repository", "description": "Repository: wwwin-github.cisco.com/smi-fuse/api-gateway\nDocker Image: dockerhub.cisco.com/smi-fuse-docker-internal/golang:1.19-alpine", "status": {"name": "To Do"}, "assignee": {"name": "cve-analysis-agent"}}}, {"key": "SMI-28011", "fields": {"summary": "Fix CVE-2023-44487 in auth-service repository", "description": "Repository: wwwin-github.cisco.com/smi-fuse/auth-service\nDocker Image: docker.io/nginx:1.20.1", "status": {"name": "To Do"}, "assignee": {"name": "cve-analysis-agent"}}}], "docker_analysis": {"dockerhub.cisco.com/smi-fuse-docker-internal/nginx:1.20.1": {"scan_successful": true, "scanner_used": "mock_scanner", "vulnerabilities": [{"cve_id": "CVE-2023-44487", "package": "nginx", "version": "1.20.1", "severity": "HIGH", "fixed_version": "1.24.0"}], "packages": [{"name": "nginx", "version": "1.20.1", "type": "deb"}, {"name": "openssl", "version": "1.1.1f", "type": "deb"}], "registry_type": "private"}, "dockerhub.cisco.com/smi-fuse-docker-internal/golang:1.19-alpine": {"scan_successful": true, "scanner_used": "mock_scanner", "vulnerabilities": [{"cve_id": "CVE-2023-44487", "package": "golang", "version": "1.19.0", "severity": "HIGH", "fixed_version": "1.21.3"}], "packages": [{"name": "golang", "version": "1.19.0", "type": "apk"}, {"name": "alpine-base", "version": "3.16.2", "type": "apk"}], "registry_type": "private"}, "docker.io/nginx:1.20.1": {"scan_successful": true, "scanner_used": "mock_scanner", "vulnerabilities": [{"cve_id": "CVE-2023-44487", "package": "nginx", "version": "1.20.1", "severity": "HIGH", "fixed_version": "1.24.0"}], "packages": [{"name": "nginx", "version": "1.20.1", "type": "deb"}, {"name": "openssl", "version": "1.1.1f", "type": "deb"}], "registry_type": "public"}}, "source_analysis": {"wwwin-github.cisco.com/smi-fuse/web-frontend": {"clone_successful": true, "dockerfile_found": true, "dockerfile_content": "FROM dockerhub.cisco.com/smi-fuse-docker-internal/nginx:1.20.1\nCOPY . /usr/share/nginx/html\nEXPOSE 80", "dependencies": [], "language": "html/javascript", "registry_type": "PRIVATE_SOURCE", "base_images": ["dockerhub.cisco.com/smi-fuse-docker-internal/nginx:1.20.1"]}, "wwwin-github.cisco.com/smi-fuse/api-gateway": {"clone_successful": true, "dockerfile_found": true, "dockerfile_content": "FROM dockerhub.cisco.com/smi-fuse-docker-internal/golang:1.19-alpine\nWORKDIR /app\nCOPY . .\nRUN go build -o main .\nEXPOSE 8080", "dependencies": [{"name": "gin-gonic/gin", "version": "v1.9.0", "file": "go.mod"}], "language": "go", "registry_type": "PRIVATE_SOURCE", "base_images": ["dockerhub.cisco.com/smi-fuse-docker-internal/golang:1.19-alpine"]}, "wwwin-github.cisco.com/smi-fuse/auth-service": {"clone_successful": true, "dockerfile_found": true, "dockerfile_content": "FROM docker.io/nginx:1.20.1\nCOPY nginx.conf /etc/nginx/nginx.conf\nCOPY . /usr/share/nginx/html\nEXPOSE 80", "dependencies": [], "language": "html/javascript", "registry_type": "PUBLIC_SOURCE", "base_images": ["docker.io/nginx:1.20.1"]}}, "validation_results": {"SMI-28009": {"classification": "TRUE_POSITIVE", "vulnerable_components": [{"component": "nginx", "current_version": "1.20.1", "fixed_version": "1.24.0", "location": "base_image"}], "registry_type": "PRIVATE_SOURCE", "remediation_strategy": "MANUAL_REVIEW"}, "SMI-28010": {"classification": "TRUE_POSITIVE", "vulnerable_components": [{"component": "nginx", "current_version": "1.20.1", "fixed_version": "1.24.0", "location": "base_image"}], "registry_type": "PRIVATE_SOURCE", "remediation_strategy": "MANUAL_REVIEW"}, "SMI-28011": {"classification": "TRUE_POSITIVE", "vulnerable_components": [{"component": "nginx", "current_version": "1.20.1", "fixed_version": "1.24.0", "location": "base_image"}], "registry_type": "PUBLIC_SOURCE", "remediation_strategy": "AUTO_FIX"}}, "remediation_actions": []}