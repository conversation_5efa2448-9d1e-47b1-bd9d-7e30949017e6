#!/usr/bin/env python3
"""
CVE Analysis and Remediation Agent - Phase 5: Automated Remediation
Applies registry-aware fixes and creates PRs for public sources
"""

import os
import sys
import json
import subprocess
import re
from typing import Dict, List, Optional
from datetime import datetime

class RemediationEngine:
    def __init__(self, analysis_results_file: str):
        """Initialize remediation engine with analysis results"""
        self.analysis_results_file = analysis_results_file
        self.analysis_data = self._load_analysis_results()
        self.git_host = "wwwin-github.cisco.com"
        self.remediation_actions = []
    
    def _load_analysis_results(self) -> Dict:
        """Load analysis results from JSON file"""
        try:
            with open(self.analysis_results_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ ERROR: [Phase 5] Failed to load analysis results: {str(e)}")
            return {}
    
    def phase_5_automated_remediation(self) -> Dict:
        """Phase 5: Apply automated remediation based on registry types"""
        print("\n🔧 PHASE 5: Automated Remediation")
        print("=" * 50)
        
        validation_results = self.analysis_data.get('validation_results', {})
        source_analysis = self.analysis_data.get('source_analysis', {})
        
        for subtask_key, validation in validation_results.items():
            if validation['classification'] != 'TRUE_POSITIVE':
                continue
            
            print(f"\n🔍 Processing {subtask_key}")
            
            # Find corresponding repository
            repo_url = self._find_repository_for_subtask(subtask_key)
            if not repo_url:
                print(f"❌ No repository found for {subtask_key}")
                continue
            
            source_data = source_analysis.get(repo_url, {})
            registry_type = validation.get('registry_type', source_data.get('registry_type', 'UNKNOWN'))

            print(f"   🏢 Registry type: {registry_type}")
            
            if registry_type == 'PUBLIC_SOURCE':
                self._apply_public_source_fix(subtask_key, repo_url, validation, source_data)
            elif registry_type == 'PRIVATE_SOURCE':
                self._provide_manual_instructions(subtask_key, repo_url, validation, source_data)
            elif registry_type == 'MIXED_SOURCE':
                self._apply_mixed_source_fix(subtask_key, repo_url, validation, source_data)
            else:
                print(f"   ⚠️ Unknown registry type, providing manual instructions")
                self._provide_manual_instructions(subtask_key, repo_url, validation, source_data)
        
        return {'remediation_actions': self.remediation_actions}
    
    def _find_repository_for_subtask(self, subtask_key: str) -> Optional[str]:
        """Find repository URL for a given subtask"""
        subtasks = self.analysis_data.get('subtasks', [])
        repositories = self.analysis_data.get('cve_info', {}).get('repositories', [])
        
        for subtask in subtasks:
            if subtask['key'] == subtask_key:
                description = subtask['fields']['description']
                for repo in repositories:
                    if any(part in description for part in repo.split('/')):
                        return repo
        return None
    
    def _apply_public_source_fix(self, subtask_key: str, repo_url: str, validation: Dict, source_data: Dict):
        """Apply automated fix for public source repositories"""
        print(f"   🔧 Applying automated fix for public source")
        
        repo_name = repo_url.split('/')[-1]
        vulnerable_components = validation.get('vulnerable_components', [])
        
        # Create fix instructions
        fix_instructions = {
            'subtask_key': subtask_key,
            'repository': repo_url,
            'registry_type': 'PUBLIC_SOURCE',
            'action_type': 'AUTOMATED_FIX',
            'changes': []
        }
        
        # Generate Dockerfile fixes
        dockerfile_content = source_data.get('dockerfile_content', '')
        updated_dockerfile = dockerfile_content
        
        for component in vulnerable_components:
            if component['location'] == 'base_image':
                # Find and update base image version
                old_image_pattern = r'FROM\s+([^\s]+)'
                matches = re.findall(old_image_pattern, dockerfile_content)
                
                for match in matches:
                    if 'docker.io' in match or not any(private in match for private in ['dockerhub.cisco.com', 'gcr.io']):
                        # This is a public image, update it
                        if component['component'] in match:
                            # Extract image name and update version
                            image_parts = match.split(':')
                            if len(image_parts) == 2:
                                base_image = image_parts[0]
                                old_version = image_parts[1]
                                new_version = component['fixed_version']
                                
                                new_image = f"{base_image}:{new_version}"
                                updated_dockerfile = updated_dockerfile.replace(match, new_image)
                                
                                fix_instructions['changes'].append({
                                    'file': 'Dockerfile',
                                    'type': 'base_image_update',
                                    'old_value': match,
                                    'new_value': new_image,
                                    'component': component['component'],
                                    'cve_fixed': self.analysis_data['cve_info']['cve_id']
                                })
        
        # Generate branch and PR information
        branch_name = subtask_key
        pr_title = f"fix({subtask_key}): Update {vulnerable_components[0]['component']} to fix {self.analysis_data['cve_info']['cve_id']}"
        
        pr_body = f"""Automated fix for {self.analysis_data['cve_info']['cve_id']} vulnerability in {vulnerable_components[0]['component']}

Changes:
- Updated {vulnerable_components[0]['component']} from version {vulnerable_components[0]['current_version']} to {vulnerable_components[0]['fixed_version']}
- Modified files: Dockerfile

Docker Image Analysis: Vulnerability confirmed in base image
Source Code Analysis: Public registry source detected
Jira: {subtask_key}
CVE: {self.analysis_data['cve_info']['cve_id']}

This is an automated security fix. Please review and merge if tests pass."""
        
        fix_instructions.update({
            'branch_name': branch_name,
            'pr_title': pr_title,
            'pr_body': pr_body,
            'updated_dockerfile': updated_dockerfile,
            'git_commands': [
                f"git clone git@{self.git_host}:{repo_url.replace(f'{self.git_host}/', '')}.git",
                f"cd {repo_name}",
                f"git checkout -b {branch_name}",
                "# Update Dockerfile with new content",
                f"git commit -m \"fix({subtask_key}): Update {vulnerable_components[0]['component']} to fix {self.analysis_data['cve_info']['cve_id']}\"",
                f"git push origin {branch_name}",
                f"gh pr create --title \"{pr_title}\" --body \"{pr_body}\""
            ]
        })
        
        self.remediation_actions.append(fix_instructions)
        print(f"   ✅ Automated fix prepared for {subtask_key}")
        print(f"   📝 Branch: {branch_name}")
        print(f"   🔗 PR would be created with title: {pr_title}")
    
    def _provide_manual_instructions(self, subtask_key: str, repo_url: str, validation: Dict, source_data: Dict):
        """Provide manual remediation instructions for private sources"""
        print(f"   📋 Providing manual instructions for private source")
        
        vulnerable_components = validation.get('vulnerable_components', [])
        
        manual_instructions = {
            'subtask_key': subtask_key,
            'repository': repo_url,
            'registry_type': 'PRIVATE_SOURCE',
            'action_type': 'MANUAL_REVIEW',
            'instructions': []
        }
        
        for component in vulnerable_components:
            instruction = {
                'component': component['component'],
                'current_version': component['current_version'],
                'fixed_version': component['fixed_version'],
                'location': component['location'],
                'steps': [
                    f"1. Contact the private registry team to update {component['component']} base image",
                    f"2. Request update from version {component['current_version']} to {component['fixed_version']} or later",
                    f"3. Update Dockerfile to use the new patched image version",
                    f"4. Test the application with the updated base image",
                    f"5. Deploy and verify the fix resolves {self.analysis_data['cve_info']['cve_id']}"
                ],
                'contacts': [
                    "Private Registry Team: <EMAIL>",
                    "Security Team: <EMAIL>"
                ]
            }
            manual_instructions['instructions'].append(instruction)
        
        self.remediation_actions.append(manual_instructions)
        print(f"   ✅ Manual instructions prepared for {subtask_key}")
        print(f"   📞 Contact required: Private registry team")
    
    def _apply_mixed_source_fix(self, subtask_key: str, repo_url: str, validation: Dict, source_data: Dict):
        """Apply partial automated fix for mixed source repositories"""
        print(f"   🔧 Applying partial automated fix for mixed source")
        
        # This would handle cases where some images are public and some are private
        # For now, treat as manual review
        self._provide_manual_instructions(subtask_key, repo_url, validation, source_data)
    
    def generate_jira_comments(self) -> Dict:
        """Generate ADF-formatted Jira comments for each subtask"""
        print("\n📝 Generating Jira comments...")
        
        jira_comments = {}
        
        for action in self.remediation_actions:
            subtask_key = action['subtask_key']
            
            if action['action_type'] == 'AUTOMATED_FIX':
                comment = self._generate_automated_fix_comment(action)
            else:
                comment = self._generate_manual_review_comment(action)
            
            jira_comments[subtask_key] = comment
            print(f"   ✅ Comment generated for {subtask_key}")
        
        return jira_comments
    
    def _generate_automated_fix_comment(self, action: Dict) -> str:
        """Generate ADF comment for automated fixes"""
        changes = action.get('changes', [])
        change_details = []
        
        for change in changes:
            change_details.append(f"* {change['file']}: {change['old_value']} → {change['new_value']}")
        
        comment = f"""🔧 AUTOMATED FIX APPLIED

📋 FIX DETAILS:
- Pull Request created: {action.get('pr_url', 'Would be created')}
- Files modified: {', '.join([c['file'] for c in changes])}
- Version changes applied:
{chr(10).join(change_details)}
- Rationale: Minimum version that patches {self.analysis_data['cve_info']['cve_id']}

📋 NEXT STEPS:
1. Review the PR: {action.get('pr_url', 'PR would be created')}
2. Run tests to verify the fix
3. Merge PR after approval
4. Update this ticket status to "Done"

🔗 PULL REQUEST:
{action.get('pr_url', 'Would be created in production environment')}

⚠️ IMPORTANT: All versions are pinned to exact releases. Review changes before merging to ensure compatibility."""
        
        return comment
    
    def _generate_manual_review_comment(self, action: Dict) -> str:
        """Generate ADF comment for manual review"""
        instructions = action.get('instructions', [])
        
        comment = f"""📋 MANUAL REMEDIATION REQUIRED

🔍 ANALYSIS RESULTS:
- Classification: TRUE POSITIVE
- Registry Type: PRIVATE SOURCE
- CVE: {self.analysis_data['cve_info']['cve_id']}
- Severity: {self.analysis_data['cve_info']['severity']}

📋 REMEDIATION STEPS:"""
        
        for i, instruction in enumerate(instructions, 1):
            comment += f"""

{i}. Component: {instruction['component']}
   Current Version: {instruction['current_version']}
   Fixed Version: {instruction['fixed_version']}
   
   Steps:
"""
            for step in instruction['steps']:
                comment += f"   {step}\n"
            
            comment += "\n   Contacts:\n"
            for contact in instruction['contacts']:
                comment += f"   - {contact}\n"
        
        comment += f"""
⚠️ IMPORTANT: This requires coordination with the private registry team to update base images.
Do not attempt to modify Dockerfile until updated base images are available.

📋 NEXT STEPS:
1. Contact private registry team
2. Request base image updates
3. Update Dockerfile once new images are available
4. Test and deploy
5. Update this ticket status to "Done" """
        
        return comment

def main():
    """Main execution for Phase 5"""
    if not os.path.exists('cve_analysis_smi_28008_results.json'):
        print("❌ ERROR: [Phase 5] Analysis results file not found")
        print("Run the CVE analysis first to generate results")
        return False
    
    engine = RemediationEngine('cve_analysis_smi_28008_results.json')
    
    try:
        # Apply remediation
        remediation_results = engine.phase_5_automated_remediation()
        
        # Generate Jira comments
        jira_comments = engine.generate_jira_comments()
        
        # Save results
        output = {
            'remediation_actions': remediation_results['remediation_actions'],
            'jira_comments': jira_comments,
            'timestamp': datetime.now().isoformat()
        }
        
        with open('remediation_results_smi_28008.json', 'w') as f:
            json.dump(output, f, indent=2)
        
        print(f"\n✅ Phase 5 Complete")
        print(f"💾 Remediation results saved to: remediation_results_smi_28008.json")
        
        # Print summary
        auto_fixes = sum(1 for action in remediation_results['remediation_actions'] 
                        if action['action_type'] == 'AUTOMATED_FIX')
        manual_reviews = len(remediation_results['remediation_actions']) - auto_fixes
        
        print(f"\n📊 REMEDIATION SUMMARY:")
        print(f"├── Automated fixes prepared: {auto_fixes}")
        print(f"├── Manual reviews required: {manual_reviews}")
        print(f"└── Jira comments generated: {len(jira_comments)}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: [Phase 5] Remediation failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
