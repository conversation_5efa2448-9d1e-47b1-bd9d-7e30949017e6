#!/usr/bin/env python3
"""
CVE Analysis and Remediation Agent - Docker Image Analysis
Analyzes Docker images for vulnerability scanning and package extraction
"""

import os
import sys
import json
import subprocess
import re
from typing import Dict, List, Optional

class DockerAnalyzer:
    def __init__(self):
        """Initialize Docker analyzer"""
        self.docker_host = "dockerhub.cisco.com/smi-fuse-docker-internal"
        
    def check_docker_daemon(self) -> bool:
        """Check if Docker daemon is running"""
        try:
            result = subprocess.run(['docker', 'info'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except Exception as e:
            print(f"❌ ERROR: [Phase 2] Docker daemon check failed: {str(e)}")
            return False
    
    def pull_docker_image(self, image_name: str) -> bool:
        """Pull Docker image from registry"""
        try:
            print(f"🐳 Pulling Docker image: {image_name}")
            result = subprocess.run(['docker', 'pull', image_name], 
                                  capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"✅ Successfully pulled: {image_name}")
                return True
            else:
                print(f"❌ ERROR: [Phase 2] Docker image pull failed for {image_name}")
                print(f"Error: {result.stderr}")
                print("Fix: Verify image exists and Docker daemon is running. Test with: docker images. Re-run after fixing.")
                return False
        except subprocess.TimeoutExpired:
            print(f"❌ ERROR: [Phase 2] Docker pull timeout for {image_name}")
            return False
        except Exception as e:
            print(f"❌ ERROR: [Phase 2] Exception pulling image: {str(e)}")
            return False
    
    def scan_image_vulnerabilities(self, image_name: str) -> Dict:
        """Scan Docker image for vulnerabilities using available scanners"""
        scan_results = {
            'vulnerabilities': [],
            'packages': [],
            'scanner_used': None,
            'scan_successful': False
        }
        
        # Try different vulnerability scanners
        scanners = [
            self._scan_with_trivy,
            self._scan_with_grype,
            self._scan_with_docker_scout
        ]
        
        for scanner in scanners:
            try:
                result = scanner(image_name)
                if result['scan_successful']:
                    scan_results.update(result)
                    break
            except Exception as e:
                print(f"⚠️ Scanner failed: {str(e)}")
                continue
        
        if not scan_results['scan_successful']:
            print("⚠️ No vulnerability scanners available, using basic package extraction")
            scan_results = self._extract_packages_basic(image_name)
        
        return scan_results
    
    def _scan_with_trivy(self, image_name: str) -> Dict:
        """Scan with Trivy vulnerability scanner"""
        try:
            # Check if trivy is available
            subprocess.run(['trivy', '--version'], capture_output=True, check=True)
            
            print(f"🔍 Scanning with Trivy: {image_name}")
            result = subprocess.run([
                'trivy', 'image', '--format', 'json', '--quiet', image_name
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                data = json.loads(result.stdout)
                return {
                    'vulnerabilities': self._parse_trivy_results(data),
                    'packages': self._extract_trivy_packages(data),
                    'scanner_used': 'trivy',
                    'scan_successful': True
                }
        except (subprocess.CalledProcessError, FileNotFoundError, json.JSONDecodeError):
            pass
        
        return {'scan_successful': False}
    
    def _scan_with_grype(self, image_name: str) -> Dict:
        """Scan with Grype vulnerability scanner"""
        try:
            # Check if grype is available
            subprocess.run(['grype', '--version'], capture_output=True, check=True)
            
            print(f"🔍 Scanning with Grype: {image_name}")
            result = subprocess.run([
                'grype', image_name, '-o', 'json'
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                data = json.loads(result.stdout)
                return {
                    'vulnerabilities': self._parse_grype_results(data),
                    'packages': self._extract_grype_packages(data),
                    'scanner_used': 'grype',
                    'scan_successful': True
                }
        except (subprocess.CalledProcessError, FileNotFoundError, json.JSONDecodeError):
            pass
        
        return {'scan_successful': False}
    
    def _scan_with_docker_scout(self, image_name: str) -> Dict:
        """Scan with Docker Scout"""
        try:
            print(f"🔍 Scanning with Docker Scout: {image_name}")
            result = subprocess.run([
                'docker', 'scout', 'cves', '--format', 'json', image_name
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                data = json.loads(result.stdout)
                return {
                    'vulnerabilities': self._parse_scout_results(data),
                    'packages': self._extract_scout_packages(data),
                    'scanner_used': 'docker_scout',
                    'scan_successful': True
                }
        except (subprocess.CalledProcessError, FileNotFoundError, json.JSONDecodeError):
            pass
        
        return {'scan_successful': False}
    
    def _extract_packages_basic(self, image_name: str) -> Dict:
        """Basic package extraction using docker run"""
        try:
            print(f"📦 Extracting packages from: {image_name}")
            
            # Try different package managers
            commands = [
                "dpkg -l",  # Debian/Ubuntu
                "rpm -qa",  # RedHat/CentOS
                "apk info", # Alpine
                "pip list", # Python packages
                "npm list -g --depth=0"  # Node.js packages
            ]
            
            packages = []
            for cmd in commands:
                try:
                    result = subprocess.run([
                        'docker', 'run', '--rm', image_name, 'sh', '-c', cmd
                    ], capture_output=True, text=True, timeout=60)
                    
                    if result.returncode == 0:
                        packages.extend(self._parse_package_output(result.stdout, cmd))
                except:
                    continue
            
            return {
                'vulnerabilities': [],
                'packages': packages,
                'scanner_used': 'basic_extraction',
                'scan_successful': True
            }
        except Exception as e:
            print(f"❌ Basic package extraction failed: {str(e)}")
            return {'scan_successful': False}
    
    def _parse_trivy_results(self, data: Dict) -> List[Dict]:
        """Parse Trivy scan results"""
        vulnerabilities = []
        for result in data.get('Results', []):
            for vuln in result.get('Vulnerabilities', []):
                vulnerabilities.append({
                    'cve_id': vuln.get('VulnerabilityID'),
                    'package': vuln.get('PkgName'),
                    'version': vuln.get('InstalledVersion'),
                    'severity': vuln.get('Severity'),
                    'fixed_version': vuln.get('FixedVersion')
                })
        return vulnerabilities
    
    def _extract_trivy_packages(self, data: Dict) -> List[Dict]:
        """Extract packages from Trivy results"""
        packages = []
        for result in data.get('Results', []):
            for pkg in result.get('Packages', []):
                packages.append({
                    'name': pkg.get('Name'),
                    'version': pkg.get('Version'),
                    'type': result.get('Type', 'unknown')
                })
        return packages
    
    def _parse_grype_results(self, data: Dict) -> List[Dict]:
        """Parse Grype scan results"""
        vulnerabilities = []
        for match in data.get('matches', []):
            vuln = match.get('vulnerability', {})
            artifact = match.get('artifact', {})
            vulnerabilities.append({
                'cve_id': vuln.get('id'),
                'package': artifact.get('name'),
                'version': artifact.get('version'),
                'severity': vuln.get('severity'),
                'fixed_version': match.get('relatedVulnerabilities', [{}])[0].get('fixedInVersion')
            })
        return vulnerabilities
    
    def _extract_grype_packages(self, data: Dict) -> List[Dict]:
        """Extract packages from Grype results"""
        packages = []
        for match in data.get('matches', []):
            artifact = match.get('artifact', {})
            packages.append({
                'name': artifact.get('name'),
                'version': artifact.get('version'),
                'type': artifact.get('type', 'unknown')
            })
        return packages
    
    def _parse_scout_results(self, data: Dict) -> List[Dict]:
        """Parse Docker Scout results"""
        # Implementation depends on Docker Scout output format
        return []
    
    def _extract_scout_packages(self, data: Dict) -> List[Dict]:
        """Extract packages from Docker Scout results"""
        # Implementation depends on Docker Scout output format
        return []
    
    def _parse_package_output(self, output: str, command: str) -> List[Dict]:
        """Parse package manager output"""
        packages = []
        lines = output.strip().split('\n')
        
        if 'dpkg -l' in command:
            for line in lines[5:]:  # Skip header lines
                parts = line.split()
                if len(parts) >= 3:
                    packages.append({
                        'name': parts[1],
                        'version': parts[2],
                        'type': 'deb'
                    })
        elif 'rpm -qa' in command:
            for line in lines:
                if line.strip():
                    # Parse RPM format: name-version-release.arch
                    match = re.match(r'^(.+)-([^-]+)-([^-]+)\.(.+)$', line.strip())
                    if match:
                        packages.append({
                            'name': match.group(1),
                            'version': f"{match.group(2)}-{match.group(3)}",
                            'type': 'rpm'
                        })
        elif 'apk info' in command:
            for line in lines:
                if line.strip():
                    packages.append({
                        'name': line.strip(),
                        'version': 'unknown',
                        'type': 'apk'
                    })
        
        return packages

def analyze_docker_images(image_list: List[str]) -> Dict:
    """Analyze multiple Docker images"""
    analyzer = DockerAnalyzer()
    
    print("🐳 PHASE 2: Docker Image Analysis")
    print("=" * 50)
    
    # Check Docker daemon
    if not analyzer.check_docker_daemon():
        print("❌ ERROR: [Phase 2] Docker daemon not running")
        print("Fix: Start Docker daemon and verify with 'docker info'. Re-run after fixing.")
        return {}
    
    results = {}
    
    for image in image_list:
        print(f"\n🔍 Analyzing image: {image}")
        
        # Pull image
        if not analyzer.pull_docker_image(image):
            results[image] = {'error': 'Failed to pull image'}
            continue
        
        # Scan for vulnerabilities
        scan_result = analyzer.scan_image_vulnerabilities(image)
        results[image] = scan_result
        
        print(f"✅ Analysis complete for {image}")
        print(f"   📦 Packages found: {len(scan_result.get('packages', []))}")
        print(f"   🚨 Vulnerabilities: {len(scan_result.get('vulnerabilities', []))}")
        print(f"   🔍 Scanner used: {scan_result.get('scanner_used', 'none')}")
    
    return results

if __name__ == "__main__":
    # Example usage
    test_images = [
        "alpine:latest",
        "ubuntu:20.04"
    ]
    
    results = analyze_docker_images(test_images)
    print(f"\n✅ Docker analysis complete for {len(results)} images")
