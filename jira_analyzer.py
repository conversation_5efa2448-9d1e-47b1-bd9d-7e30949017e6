#!/usr/bin/env python3
"""
CVE Analysis and Remediation Agent - Jira Integration
Analyzes Jira tickets for CVE vulnerabilities and manages remediation workflow
"""

import os
import sys
import json
import requests
from urllib.parse import urlparse
import subprocess
import re
from typing import Dict, List, Optional, Tuple

class JiraAnalyzer:
    def __init__(self, jira_url: str = None, username: str = None, token: str = None):
        """Initialize Jira analyzer with authentication"""
        self.jira_url = jira_url or os.getenv('JIRA_URL', 'https://jira-eng-gpk2.cisco.com')
        self.username = username or os.getenv('JIRA_USERNAME')
        self.token = token or os.getenv('JIRA_TOKEN')
        self.session = requests.Session()
        
        if self.username and self.token:
            self.session.auth = (self.username, self.token)
        
        self.headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    
    def get_ticket_details(self, ticket_id: str) -> Dict:
        """Get detailed information about a Jira ticket"""
        try:
            url = f"{self.jira_url}/rest/api/2/issue/{ticket_id}"
            response = self.session.get(url, headers=self.headers)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ ERROR: [Phase 1] Failed to fetch ticket {ticket_id}: {response.status_code}")
                print(f"Response: {response.text}")
                return {}
        except Exception as e:
            print(f"❌ ERROR: [Phase 1] Exception fetching ticket {ticket_id}: {str(e)}")
            return {}
    
    def get_subtasks(self, parent_ticket_id: str) -> List[Dict]:
        """Get all sub-tasks linked to parent ticket"""
        try:
            # Search for sub-tasks using JQL
            jql = f"parent = {parent_ticket_id}"
            url = f"{self.jira_url}/rest/api/2/search"
            params = {
                'jql': jql,
                'fields': 'key,summary,description,status,assignee,components'
            }
            
            response = self.session.get(url, headers=self.headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                return data.get('issues', [])
            else:
                print(f"❌ ERROR: [Phase 1] Failed to fetch sub-tasks for {parent_ticket_id}: {response.status_code}")
                return []
        except Exception as e:
            print(f"❌ ERROR: [Phase 1] Exception fetching sub-tasks: {str(e)}")
            return []
    
    def extract_cve_info(self, ticket_data: Dict) -> Dict:
        """Extract CVE information from ticket description and summary"""
        cve_info = {
            'cve_id': None,
            'component': None,
            'severity': None,
            'description': '',
            'docker_images': [],
            'repositories': []
        }
        
        # Get ticket content
        summary = ticket_data.get('fields', {}).get('summary', '')
        description = ticket_data.get('fields', {}).get('description', '')
        
        # Extract CVE ID using regex
        cve_pattern = r'CVE-\d{4}-\d{4,7}'
        cve_matches = re.findall(cve_pattern, summary + ' ' + description)
        if cve_matches:
            cve_info['cve_id'] = cve_matches[0]
        
        # Extract Docker images
        docker_patterns = [
            r'dockerhub\.cisco\.com/[^\s]+',
            r'docker\.io/[^\s]+',
            r'gcr\.io/[^\s]+',
            r'quay\.io/[^\s]+'
        ]
        
        for pattern in docker_patterns:
            matches = re.findall(pattern, description)
            cve_info['docker_images'].extend(matches)
        
        # Extract repository URLs
        repo_patterns = [
            r'wwwin-github\.cisco\.com/[^\s]+',
            r'github\.com/[^\s]+',
            r'git@wwwin-github\.cisco\.com:[^\s]+'
        ]
        
        for pattern in repo_patterns:
            matches = re.findall(pattern, description)
            cve_info['repositories'].extend(matches)
        
        cve_info['description'] = description
        
        return cve_info
    
    def assign_subtask_to_me(self, subtask_key: str, assignee: str = None) -> bool:
        """Assign subtask to specified user (or current user)"""
        try:
            if not assignee:
                # Get current user
                user_url = f"{self.jira_url}/rest/api/2/myself"
                user_response = self.session.get(user_url, headers=self.headers)
                if user_response.status_code == 200:
                    assignee = user_response.json().get('name')
                else:
                    print(f"❌ ERROR: Could not get current user info")
                    return False
            
            url = f"{self.jira_url}/rest/api/2/issue/{subtask_key}/assignee"
            data = {"name": assignee}
            
            response = self.session.put(url, headers=self.headers, json=data)
            
            if response.status_code in [200, 204]:
                print(f"✅ Assigned {subtask_key} to {assignee}")
                return True
            else:
                print(f"❌ Failed to assign {subtask_key}: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ ERROR: Exception assigning subtask: {str(e)}")
            return False

def analyze_ticket_smi_28008():
    """Analyze specific ticket SMI-28008"""
    print("🔍 CVE ANALYSIS STARTING: SMI-28008")
    print("=" * 50)
    
    # Initialize Jira analyzer
    analyzer = JiraAnalyzer()
    
    # Get parent ticket details
    print("📋 PHASE 1: Jira Analysis")
    parent_ticket = analyzer.get_ticket_details("SMI-28008")
    
    if not parent_ticket:
        print("❌ ERROR: [Phase 1] Could not fetch parent ticket SMI-28008")
        print("Fix: Verify Jira credentials and ticket exists. Re-run after fixing.")
        return False
    
    print(f"✅ Parent ticket fetched: {parent_ticket.get('key', 'SMI-28008')}")
    
    # Extract CVE information
    cve_info = analyzer.extract_cve_info(parent_ticket)
    print(f"🔍 CVE ID: {cve_info['cve_id']}")
    print(f"🐳 Docker Images: {cve_info['docker_images']}")
    print(f"📁 Repositories: {cve_info['repositories']}")
    
    # Get sub-tasks
    subtasks = analyzer.get_subtasks("SMI-28008")
    
    if not subtasks:
        print("❌ ERROR: [Phase 1] No sub-tasks found.")
        print("Fix: Verify parent ticket has linked sub-tasks. Re-run after fixing.")
        return False
    
    print(f"✅ Found {len(subtasks)} sub-tasks")
    
    # Process each sub-task
    for subtask in subtasks:
        subtask_key = subtask['key']
        print(f"\n📋 Processing sub-task: {subtask_key}")
        
        # Check if assigned to me, if not assign it
        assignee = subtask.get('fields', {}).get('assignee')
        if not assignee:
            print(f"🔄 Assigning {subtask_key} to current user...")
            analyzer.assign_subtask_to_me(subtask_key)
        
        # Extract repository info from sub-task
        subtask_cve_info = analyzer.extract_cve_info(subtask)
        print(f"   📁 Repository: {subtask_cve_info['repositories']}")
        print(f"   🐳 Docker Images: {subtask_cve_info['docker_images']}")
    
    return {
        'parent_ticket': parent_ticket,
        'cve_info': cve_info,
        'subtasks': subtasks
    }

if __name__ == "__main__":
    # Check for required environment variables
    if not os.getenv('JIRA_USERNAME') or not os.getenv('JIRA_TOKEN'):
        print("❌ ERROR: Missing Jira credentials")
        print("Please set JIRA_USERNAME and JIRA_TOKEN environment variables")
        print("Example:")
        print("export JIRA_USERNAME='your-username'")
        print("export JIRA_TOKEN='your-api-token'")
        sys.exit(1)
    
    result = analyze_ticket_smi_28008()
    if result:
        print("\n✅ Phase 1 Complete - Ready for Phase 2: Docker Image Analysis")
    else:
        print("\n❌ Phase 1 Failed - Fix errors before proceeding")
