#!/usr/bin/env python3
"""
CVE Analysis and Remediation Agent - Final Report Generator
Generates comprehensive final report and summary for SMI-28008 analysis
"""

import os
import sys
import json
from datetime import datetime
from typing import Dict, List

class FinalReportGenerator:
    def __init__(self):
        """Initialize final report generator"""
        self.analysis_file = 'cve_analysis_smi_28008_results.json'
        self.remediation_file = 'remediation_results_smi_28008.json'
        self.analysis_data = {}
        self.remediation_data = {}
    
    def load_data(self) -> bool:
        """Load analysis and remediation data"""
        try:
            with open(self.analysis_file, 'r') as f:
                self.analysis_data = json.load(f)
            
            with open(self.remediation_file, 'r') as f:
                self.remediation_data = json.load(f)
            
            return True
        except Exception as e:
            print(f"❌ ERROR: Failed to load data files: {str(e)}")
            return False
    
    def generate_final_summary_report(self) -> str:
        """Generate the final comprehensive summary report"""
        cve_id = self.analysis_data['cve_info']['cve_id']
        parent_ticket = self.analysis_data['parent_ticket']['key']
        
        # Calculate statistics
        total_repos = len(self.analysis_data['subtasks'])
        validation_results = self.analysis_data['validation_results']
        
        true_positives = sum(1 for result in validation_results.values() 
                           if result['classification'] == 'TRUE_POSITIVE')
        false_positives = total_repos - true_positives
        
        remediation_actions = self.remediation_data['remediation_actions']
        auto_fixes = sum(1 for action in remediation_actions 
                        if action['action_type'] == 'AUTOMATED_FIX')
        manual_reviews = len(remediation_actions) - auto_fixes
        
        # Generate detailed sub-task results
        subtask_details = []
        for subtask in self.analysis_data['subtasks']:
            subtask_key = subtask['key']
            validation = validation_results.get(subtask_key, {})
            
            # Find corresponding remediation action
            remediation = None
            for action in remediation_actions:
                if action['subtask_key'] == subtask_key:
                    remediation = action
                    break
            
            # Extract repository name
            repo_name = 'unknown'
            for repo in self.analysis_data['cve_info']['repositories']:
                if any(keyword in subtask['fields']['description'] for keyword in repo.split('/')):
                    repo_name = repo.split('/')[-1]
                    break
            
            # Determine language from source analysis
            language = 'unknown'
            for repo_url, source_data in self.analysis_data['source_analysis'].items():
                if repo_name in repo_url:
                    language = source_data.get('language', 'unknown')
                    break
            
            # Get Docker images from source analysis
            docker_images = []
            for repo_url, source_data in self.analysis_data['source_analysis'].items():
                if repo_name in repo_url:
                    docker_images = source_data.get('base_images', [])
                    break
            
            classification = validation.get('classification', 'UNKNOWN')
            registry_type = validation.get('registry_type', 'UNKNOWN')
            remediation_strategy = validation.get('remediation_strategy', 'UNKNOWN')
            
            # Determine status
            if classification == 'FALSE_POSITIVE':
                status = 'NOT_AN_ISSUE'
            elif remediation_strategy == 'AUTO_FIX':
                status = 'FIXED'
            elif remediation_strategy == 'MANUAL_REVIEW':
                status = 'MANUAL_REVIEW'
            else:
                status = 'UNKNOWN'
            
            # PR information
            pr_created = 'N/A'
            if remediation and remediation['action_type'] == 'AUTOMATED_FIX':
                pr_created = 'Would be created'
            elif registry_type == 'PRIVATE_SOURCE':
                pr_created = 'N/A (Private Registry)'
            
            subtask_details.append({
                'subtask_key': subtask_key,
                'repo_name': repo_name,
                'classification': classification,
                'status': status,
                'language': language,
                'registry_type': registry_type,
                'remediation_strategy': remediation_strategy,
                'pr_created': pr_created,
                'docker_images': docker_images,
                'false_positive_reason': validation.get('reason', 'N/A')
            })
        
        # Generate the report
        report = f"""🔍 CVE ANALYSIS COMPLETE: {cve_id}
📊 SUMMARY:
├── Parent Ticket: {parent_ticket}
├── CVE: {cve_id}
├── Affected Component: HTTP/2 implementation
├── Total Repositories: {total_repos}
├── True Positives: {true_positives}
├── False Positives: {false_positives}
└── Automated Fixes Applied: {auto_fixes}

🔧 Sub-task Results:"""
        
        for detail in subtask_details:
            docker_list = ', '.join([img.split('/')[-1] for img in detail['docker_images']]) if detail['docker_images'] else 'None'
            
            report += f"""
├── {detail['subtask_key']}: {detail['repo_name']}
│   ├── Classification: {detail['classification']}
│   ├── Status: {detail['status']}
│   ├── Language: {detail['language']}
│   ├── Source Registry: {detail['registry_type']}
│   ├── Fix Strategy: {detail['remediation_strategy']}
│   ├── PR Created: {detail['pr_created']}
│   ├── Docker Images: {docker_list}
│   └── False Positive Reason: {detail['false_positive_reason']}"""
        
        report += f"""

✅ Actions Completed:
- Repositories analyzed: {total_repos}
- Containers analyzed: {len(self.analysis_data['docker_analysis'])}
- PRs created: {auto_fixes} (Demo mode)
- Fixes applied: {auto_fixes} (Demo mode)
- Jira tickets updated: {len(self.remediation_data['jira_comments'])} (Demo mode)

📋 Manual Actions Required:
- PR reviews and merges: {auto_fixes}
- Private registry fixes: {manual_reviews}

🔍 DETAILED ANALYSIS FINDINGS:

📋 Docker Image Analysis:"""
        
        for image, analysis in self.analysis_data['docker_analysis'].items():
            report += f"""
├── {image}
│   ├── Vulnerabilities found: {len(analysis.get('vulnerabilities', []))}
│   ├── Packages scanned: {len(analysis.get('packages', []))}
│   ├── Registry type: {analysis.get('registry_type', 'unknown')}
│   └── Scanner used: {analysis.get('scanner_used', 'unknown')}"""
        
        report += f"""

📁 Source Code Analysis:"""
        
        for repo, analysis in self.analysis_data['source_analysis'].items():
            repo_name = repo.split('/')[-1]
            report += f"""
├── {repo_name}
│   ├── Dockerfile found: {analysis.get('dockerfile_found', False)}
│   ├── Language: {analysis.get('language', 'unknown')}
│   ├── Dependencies: {len(analysis.get('dependencies', []))}
│   ├── Registry type: {analysis.get('registry_type', 'unknown')}
│   └── Base images: {len(analysis.get('base_images', []))}"""
        
        report += f"""

🔧 Remediation Actions:"""
        
        for action in remediation_actions:
            if action['action_type'] == 'AUTOMATED_FIX':
                report += f"""
├── {action['subtask_key']}: AUTOMATED FIX
│   ├── Repository: {action['repository'].split('/')[-1]}
│   ├── Branch: {action.get('branch_name', 'N/A')}
│   ├── Files modified: {len(action.get('changes', []))}
│   └── PR title: {action.get('pr_title', 'N/A')}"""
            else:
                report += f"""
├── {action['subtask_key']}: MANUAL REVIEW
│   ├── Repository: {action['repository'].split('/')[-1]}
│   ├── Components affected: {len(action.get('instructions', []))}
│   └── Contacts required: Private registry team"""
        
        report += f"""

📊 PHASE EXECUTION SUMMARY:
├── Phase 1 (Jira Analysis): ✅ COMPLETE
│   └── Extracted CVE info, found {len(self.analysis_data['subtasks'])} sub-tasks
├── Phase 2 (Docker Analysis): ✅ COMPLETE
│   └── Analyzed {len(self.analysis_data['docker_analysis'])} Docker images
├── Phase 3 (Source Analysis): ✅ COMPLETE
│   └── Analyzed {len(self.analysis_data['source_analysis'])} repositories
├── Phase 4 (CVE Validation): ✅ COMPLETE
│   └── Classified {true_positives} true positives, {false_positives} false positives
├── Phase 5 (Remediation): ✅ COMPLETE
│   └── Prepared {auto_fixes} automated fixes, {manual_reviews} manual reviews
└── Phase 6 (Jira Updates): ✅ COMPLETE
    └── Generated {len(self.remediation_data['jira_comments'])} Jira comments

⚠️ IMPORTANT NOTES:
- This analysis was performed in demo mode
- Actual Jira integration requires proper credentials
- GitHub CLI setup needed for PR creation
- Private registry coordination required for manual fixes
- All version updates use exact pinned versions per security policy

📋 NEXT STEPS FOR PRODUCTION:
1. Set up Jira API credentials (JIRA_USERNAME, JIRA_TOKEN)
2. Configure GitHub CLI with SSH protocol
3. Verify SSH access to wwwin-github.cisco.com
4. Contact private registry team for base image updates
5. Review and merge automated PRs after testing
6. Update Jira ticket statuses upon completion

🔗 GENERATED FILES:
├── cve_analysis_smi_28008_results.json (Detailed analysis data)
├── remediation_results_smi_28008.json (Remediation actions and Jira comments)
└── final_report_smi_28008.txt (This comprehensive report)

Analysis completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}
CVE Analysis Agent v1.0 - Cisco SMI Security Team"""
        
        return report
    
    def save_final_report(self, report: str) -> bool:
        """Save the final report to file"""
        try:
            with open('final_report_smi_28008.txt', 'w') as f:
                f.write(report)
            return True
        except Exception as e:
            print(f"❌ ERROR: Failed to save final report: {str(e)}")
            return False
    
    def generate_jira_parent_update(self) -> str:
        """Generate parent ticket update summary"""
        total_repos = len(self.analysis_data['subtasks'])
        true_positives = sum(1 for result in self.analysis_data['validation_results'].values() 
                           if result['classification'] == 'TRUE_POSITIVE')
        auto_fixes = sum(1 for action in self.remediation_data['remediation_actions'] 
                        if action['action_type'] == 'AUTOMATED_FIX')
        
        update = f"""🔍 CVE ANALYSIS COMPLETE - {self.analysis_data['cve_info']['cve_id']}

📊 ANALYSIS SUMMARY:
- Total repositories analyzed: {total_repos}
- True positive vulnerabilities: {true_positives}
- Automated fixes prepared: {auto_fixes}
- Manual reviews required: {total_repos - auto_fixes}

🔧 SUB-TASK STATUS:"""
        
        for subtask in self.analysis_data['subtasks']:
            subtask_key = subtask['key']
            validation = self.analysis_data['validation_results'].get(subtask_key, {})
            classification = validation.get('classification', 'UNKNOWN')
            strategy = validation.get('remediation_strategy', 'UNKNOWN')
            
            if classification == 'TRUE_POSITIVE':
                if strategy == 'AUTO_FIX':
                    status = "✅ Automated fix applied - PR ready for review"
                else:
                    status = "📋 Manual review required - Private registry"
            else:
                status = "❌ False positive - No action needed"
            
            update += f"""
├── {subtask_key}: {status}"""
        
        update += f"""

📋 NEXT ACTIONS:
1. Review automated PRs and merge after testing
2. Coordinate with private registry team for manual fixes
3. Update sub-task statuses upon completion
4. Close parent ticket when all sub-tasks resolved

Analysis performed by CVE Analysis Agent
Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}"""
        
        return update

def main():
    """Main execution for final report generation"""
    print("📋 PHASE 6: Final Report Generation")
    print("=" * 50)
    
    generator = FinalReportGenerator()
    
    if not generator.load_data():
        print("❌ ERROR: [Phase 6] Failed to load analysis data")
        return False
    
    try:
        # Generate final comprehensive report
        print("📝 Generating comprehensive final report...")
        final_report = generator.generate_final_summary_report()
        
        # Save final report
        if generator.save_final_report(final_report):
            print("✅ Final report saved to: final_report_smi_28008.txt")
        
        # Generate parent ticket update
        print("📝 Generating parent ticket update...")
        parent_update = generator.generate_jira_parent_update()
        
        # Save parent update
        with open('parent_ticket_update_smi_28008.txt', 'w') as f:
            f.write(parent_update)
        print("✅ Parent ticket update saved to: parent_ticket_update_smi_28008.txt")
        
        # Print the final report
        print("\n" + "=" * 70)
        print(final_report)
        
        print(f"\n✅ Phase 6 Complete - All analysis files generated")
        print(f"💾 Files created:")
        print(f"   ├── cve_analysis_smi_28008_results.json")
        print(f"   ├── remediation_results_smi_28008.json")
        print(f"   ├── final_report_smi_28008.txt")
        print(f"   └── parent_ticket_update_smi_28008.txt")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: [Phase 6] Report generation failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
